// Employee login page
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Employee } from '../types/auth.types';
import { useAuth } from '../hooks/useAuth';
import { useEmployees } from '../hooks/useEmployees';
import { EmployeeSelector } from '../components/features/EmployeeSelector';
import { PinKeypad } from '../components/features/PinKeypad';
import { MotivationalQuotes } from '../components/features/MotivationalQuotes';
import { Button } from '../components/common/Button';

const loginSchema = z.object({
  employeeId: z.string().min(1, 'Çalışan seçimi gereklidir'),
  pin: z.string().length(6, 'PIN 6 haneli olmalıdır'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { login, isLoading: authLoading, isAuthenticated } = useAuth();
  const { employees, isLoading: employeesLoading } = useEmployees();
  
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [pin, setPin] = useState('');

  const {
    handleSubmit,
    setValue,
    formState: { errors, isValid },
    watch,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
  });

  const watchedEmployeeId = watch('employeeId');
  const watchedPin = watch('pin');

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Update form when employee is selected
  useEffect(() => {
    if (selectedEmployee) {
      setValue('employeeId', selectedEmployee.id);
    }
  }, [selectedEmployee, setValue]);

  // Update form when PIN changes
  useEffect(() => {
    setValue('pin', pin);
  }, [pin, setValue]);

  const onSubmit = async (data: LoginFormData) => {
    try {
      await login(data);
      navigate('/dashboard');
    } catch (error) {
      // Error handling is done in useAuth hook
      setPin(''); // Clear PIN on error
    }
  };

  const handleEmployeeSelect = (employee: Employee) => {
    setSelectedEmployee(employee);
    setPin(''); // Clear PIN when changing employee
  };

  const isFormValid = selectedEmployee && pin.length === 6;

  return (
    <div className="min-h-screen flex">
      {/* Left side - Background image with quotes */}
      <div 
        className="hidden lg:flex lg:w-1/2 bg-cover bg-center relative"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 to-purple-900/80" />
        <div className="relative z-10 flex flex-col justify-center items-start p-12">
          <MotivationalQuotes />
        </div>
      </div>

      {/* Right side - Login form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-8 bg-gray-50">
        <div className="w-full max-w-md">
          {/* Logo and title */}
          <div className="text-center mb-8">
            <div className="mb-4">
              <h1 className="text-2xl font-bold text-gray-900">Aleo.</h1>
              <p className="text-sm text-gray-500">POS SYSTEM</p>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Employee Login</h2>
            <p className="text-sm text-gray-600">
              Please select your account to start your shift
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Employee Selector */}
            <EmployeeSelector
              employees={employees}
              selectedEmployee={selectedEmployee}
              onEmployeeSelect={handleEmployeeSelect}
              isLoading={employeesLoading}
            />

            {/* PIN Keypad - Only show when employee is selected */}
            {selectedEmployee && (
              <div className="animate-fadeIn">
                <PinKeypad
                  pin={pin}
                  onPinChange={setPin}
                  maxLength={6}
                  disabled={authLoading}
                />
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={!isFormValid || authLoading}
              loading={authLoading}
              className="w-full"
              size="lg"
            >
              Vardiyayı Başlat
            </Button>

            {/* Error Messages */}
            {errors.employeeId && (
              <p className="text-red-600 text-sm">{errors.employeeId.message}</p>
            )}
            {errors.pin && (
              <p className="text-red-600 text-sm">{errors.pin.message}</p>
            )}
          </form>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500">
              Atorpos POS System v1.0.0
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
