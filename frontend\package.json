{"name": "frontend", "version": "1.0.0", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "build-electron": "tsc electron/main.ts --outDir dist-electron --target ES2020 --module commonjs --esModuleInterop", "electron": "npm run build-electron && electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && npm run build-electron && electron .\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@hookform/resolvers": "^5.1.1", "@types/react-router-dom": "^5.3.3", "axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.3", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "electron": "^37.2.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^7.0.4", "wait-on": "^8.0.3"}}